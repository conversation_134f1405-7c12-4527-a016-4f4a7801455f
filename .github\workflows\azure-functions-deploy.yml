name: Deploy Azure Functions

on:
  push:
    branches: [ main ]
    paths:
      - 'azure_functions/**'
  workflow_dispatch:  # Allow manual triggering

env:
  AZURE_FUNCTIONAPP_NAME: careerbot-functions  # Change this to your function app name
  AZURE_FUNCTIONAPP_PACKAGE_PATH: 'azure_functions'
  PYTHON_VERSION: '3.9'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Setup Python
      uses: actions/setup-python@v2
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}/requirements.txt

    - name: Deploy to Azure Functions
      uses: Azure/functions-action@v1
      with:
        app-name: ${{ env.AZURE_FUNCTIONAPP_NAME }}
        package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE }}
        # If you don't have a publish profile secret, you can use service principal authentication:
        # creds: ${{ secrets.AZURE_CREDENTIALS }}
