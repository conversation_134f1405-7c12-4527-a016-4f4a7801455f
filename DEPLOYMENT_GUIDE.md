# Azure Deployment Guide

This guide explains how to deploy your chatbot application to Azure using Azure Functions and Azure Static Web Apps.

## Prerequisites

- Azure CLI installed (Download from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
- PowerShell 5.1 or higher
- An active Azure subscription

## Deployment Steps

1. **Create a .env file with your environment variables**

   If you don't already have a .env file, create one based on the .env.example template:

   ```
   # Azure Cosmos DB
   COSMOS_ENDPOINT=https://your-cosmos-account.documents.azure.com:443/
   COSMOS_KEY=your-cosmos-key
   COSMOS_DATABASE=CareerGuidanceDB
   COSMOS_CONTAINER=UserPreferences

   # Other configuration variables
   FLASK_APP=app.py
   FLASK_ENV=production
   SECRET_KEY=your-secret-key
   ```

   Note: The deployment script will create a new Cosmos DB instance, so you don't need to fill in the Cosmos DB values.

2. **Package your function app**

   Run the packaging script to prepare your application for deployment:

   ```powershell
   .\package_function_app.ps1
   ```

   This script will:
   - Create a function_app directory
   - Copy your application files, including models and templates
   - Package everything into function_app.zip

3. **Deploy to Azure**

   Run the deployment script:

   ```powershell
   .\deploy_to_azure.ps1
   ```

   This script will:
   - Create a resource group
   - Create a storage account
   - Create a Cosmos DB account with database and container
   - Create an Azure Functions app
   - Create an Azure Static Web App
   - Configure environment variables
   - Deploy your function app and static web app

4. **Verify Deployment**

   After deployment completes, you'll see URLs for:
   - Function App (backend API)
   - Static Web App (frontend)

   Visit these URLs to verify that your application is running correctly.

## Using Your ML Models

The deployment includes your ML models located in the `models/` directory. When your Flask app is running in Azure Functions, it will load and use these models for predictions instead of hardcoded responses.

Make sure your app.py and prediction.py files correctly reference the models:

```python
# Example of loading a model in prediction.py
model_path = os.path.join(os.path.dirname(__file__), 'models', 'your_model.joblib')
model = joblib.load(model_path)
```

## Troubleshooting

If you encounter issues:

1. Check the Azure Portal for error logs
2. Verify that all required files were included in the function_app.zip package
3. Check that environment variables are configured correctly in the Azure Function app
4. Ensure your models are properly loaded and referenced in your code

## Additional Resources

- [Azure Functions Documentation](https://docs.microsoft.com/en-us/azure/azure-functions/)
- [Azure Static Web Apps Documentation](https://docs.microsoft.com/en-us/azure/static-web-apps/)
- [Azure Cosmos DB Documentation](https://docs.microsoft.com/en-us/azure/cosmos-db/) 