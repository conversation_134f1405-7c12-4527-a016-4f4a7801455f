<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Career Guidance Chatbot - Powered by <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }
        .main-container {
            height: 100vh;
            display: flex;
        }
        .sidebar {
            width: 280px;
            background-color: #2c3e50;
            color: white;
            padding: 15px;
            overflow-y: auto;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .sidebar-header {
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }
        .sidebar-header h5 {
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
            color: #ecf0f1;
        }
        .sidebar-header p {
            opacity: 0.7;
            font-size: 0.8rem;
        }
        .sidebar-toggle {
            position: fixed;
            left: 280px;
            top: 15px;
            background-color: #2c3e50;
            color: white;
            border: none;
            border-radius: 0 8px 8px 0;
            padding: 12px 8px;
            z-index: 1001;
            transition: all 0.3s;
            box-shadow: 3px 0 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .sidebar-toggle:hover {
            background-color: #3498db;
        }
        .sidebar-toggle i {
            font-size: 1.1rem;
        }
        .sidebar.collapsed {
            left: -280px;
        }
        .sidebar-toggle.collapsed {
            left: 0;
        }
        .user-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .user-item {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            background-color: rgba(255,255,255,0.05);
        }
        .user-item:hover {
            background-color: rgba(255,255,255,0.1);
            border-left-color: rgba(255,255,255,0.5);
            transform: translateX(2px);
        }
        .user-item.active {
            background-color: #3498db;
            border-left-color: #ffffff;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }
        .user-item .user-id {
            font-weight: 600;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            color: #ecf0f1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .user-item .user-id::before {
            content: '';
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #2ecc71;
            margin-right: 10px;
            box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
        }
        .user-item .timestamp {
            font-size: 0.75rem;
            color: rgba(255,255,255,0.7);
            margin-left: 18px;
            margin-top: 4px;
            font-style: italic;
        }
        .chat-container {
            flex: 1;
            padding: 25px;
            margin-left: 280px;
            transition: all 0.3s;
            max-width: 1200px;
            margin: 0 auto 0 280px;
            background-color: #f8f9fa;
        }
        .chat-container.expanded {
            margin-left: 0;
        }
        .chat-box {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            background-color: white;
            margin-bottom: 20px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
        }
        .user-message {
            background-color: #3498db;
            color: white;
            border-radius: 18px 18px 0 18px;
            padding: 12px 18px;
            margin-bottom: 15px;
            max-width: 70%;
            margin-left: auto;
            box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
            animation: fadeIn 0.3s ease;
        }
        .bot-message {
            background-color: #f1f3f5;
            color: #343a40;
            border-radius: 18px 18px 18px 0;
            padding: 15px 20px;
            margin-bottom: 15px;
            max-width: 85%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border-left: 3px solid #4285f4;
            animation: fadeIn 0.3s ease;
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 0.95rem;
        }
        .bot-message h4 {
            color: #4285f4;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        .bot-message h5 {
            color: #2c3e50;
            margin-top: 15px;
            margin-bottom: 8px;
            font-size: 1rem;
        }
        .bot-message ul {
            margin-left: 15px;
            margin-bottom: 10px;
        }
        .bot-message li {
            margin-bottom: 5px;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .loading {
            display: none;
            text-align: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            background-color: rgba(66, 133, 244, 0.05);
        }
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            color: #4285f4;
        }
        .gemini-badge {
            background-color: #4285f4;
            color: white;
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
            margin-left: 10px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        .btn {
            white-space: nowrap;
            border-radius: 8px;
            padding: 10px 18px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .btn-primary {
            background-color: #4285f4;
            border-color: #4285f4;
        }
        .btn-primary:hover {
            background-color: #3367d6;
            border-color: #3367d6;
        }
        .btn-danger {
            background-color: #ea4335;
            border-color: #ea4335;
        }
        .btn-danger:hover {
            background-color: #d33b2c;
            border-color: #d33b2c;
        }
        #newChatBtn {
            background-color: #34a853;
            border-color: #34a853;
            transition: all 0.2s;
            padding: 6px 12px;
            font-size: 0.9rem;
        }
        #newChatBtn:hover {
            background-color: #2d8f47;
            border-color: #2d8f47;
        }
        .input-group {
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        .form-control {
            border: 1px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
        }
        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.25);
            border-color: #4285f4;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            font-size: 0.85rem;
            color: #7f8c8d;
            border-top: 1px solid #e9ecef;
        }
        /* Responsive styles */
        @media (max-width: 768px) {
            .sidebar {
                left: -280px;
            }
            .sidebar-toggle {
                left: 0;
            }
            .chat-container {
                margin-left: 0;
            }
            .sidebar.active {
                left: 0;
            }
            .sidebar-toggle.active {
                left: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar for user chats -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="mb-0">Chat History</h5>
                    <button class="btn btn-sm btn-success" id="newChatBtn">
                        <i class="bi bi-plus-lg"></i> New Chat
                    </button>
                </div>
                <p>Select a chat to view its history</p>
            </div>
            <div id="sidebarLoading" class="text-center my-3">
                <div class="spinner-border spinner-border-sm text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Loading chats...</span>
            </div>
            <ul class="user-list" id="userList">
                <!-- User list will be populated here -->
            </ul>
        </div>

        <!-- Sidebar toggle button -->
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="bi bi-chevron-left" id="toggleIcon"></i>
        </button>

        <!-- Main chat area -->
        <div class="chat-container" id="chatContainer">
            <h1 class="text-center mb-4">AI Career Guidance Chatbot <span class="gemini-badge">Powered by Gemini 2.0 Flash</span></h1>



            <div class="d-flex justify-content-end align-items-center mb-4">
                <button class="btn btn-danger me-3" id="clearChatBtn">
                    <i class="bi bi-trash me-1"></i> Clear Chat
                </button>
                <div id="syncStatus" class="text-success me-2 d-flex align-items-center" style="display: none;">
                    <i class="bi bi-check-circle-fill me-1"></i>
                    <span>Synced across browsers</span>
                </div>
            </div>

            <div class="chat-box" id="chatBox">
                <!-- Chat messages will appear here -->
                <div class="bot-message" id="welcomeMessage">
                    Hello! I'm your AI-powered career guidance assistant. I can help you with:

                    🎯 Career recommendations based on your skills
                    🗺️ Complete career roadmaps
                    📚 Study material recommendations
                    🎓 Course suggestions from platforms like Udemy, Coursera, etc.
                    💼 Interview preparation guidance

                    Tell me about your skills or ask about any career field!
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Thinking with Gemini AI...</span>
            </div>

            <div class="input-group mb-4">
                <input type="text" class="form-control" id="userInput" placeholder="Ask about careers, skills, or say 'I know Python and data analysis'">
                <button class="btn btn-primary" type="button" id="sendBtn">
                    <i class="bi bi-send-fill me-1"></i> Send
                </button>
            </div>

            <div class="footer">
                Powered by Google Gemini 2.0 Flash Model for Advanced Career Guidance
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM elements
            const chatBox = document.getElementById('chatBox');
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');
            const loading = document.getElementById('loading');
            const clearChatBtn = document.getElementById('clearChatBtn');
            const syncStatus = document.getElementById('syncStatus');
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const toggleIcon = document.getElementById('toggleIcon');
            const chatContainer = document.getElementById('chatContainer');
            const userList = document.getElementById('userList');
            const sidebarLoading = document.getElementById('sidebarLoading');
            const newChatBtn = document.getElementById('newChatBtn');
            // User ID and API Key management
            const userId = "{{ user_id }}";
            let currentUserId = userId;

            // TODO: Replace 'YOUR_GEMINI_API_KEY_HERE' with your actual Gemini API key
            // Get your API key from: https://makersuite.google.com/app/apikey
            const geminiApiKey = 'AIzaSyCsoHUwWqVakaA1hh0fh6HxfMXpCbM3Vdo;

            // Store the user ID in localStorage for cross-browser access
            localStorage.setItem('chatbotUserId', userId);

            // Show sync status
            syncStatus.style.display = 'block';

            // Load chat history from server if available
            const chatHistory = {{ chat_history|safe }};
            if (chatHistory && chatHistory.length > 0) {
                // Clear default welcome message
                chatBox.innerHTML = '';

                // Add messages from history
                chatHistory.forEach(msg => {
                    addMessage(msg.content, msg.role === 'user');
                });
            }



            // Toggle sidebar
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                chatContainer.classList.toggle('expanded');
                sidebarToggle.classList.toggle('collapsed');

                if (sidebar.classList.contains('collapsed')) {
                    toggleIcon.classList.remove('bi-chevron-left');
                    toggleIcon.classList.add('bi-chevron-right');
                } else {
                    toggleIcon.classList.remove('bi-chevron-right');
                    toggleIcon.classList.add('bi-chevron-left');
                }
            });

            // Function to add a message to the chat box
            function addMessage(message, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = isUser ? 'user-message' : 'bot-message';

                if (isUser) {
                    messageDiv.textContent = message;
                } else {
                    // For bot messages, preserve formatting
                    messageDiv.innerHTML = formatBotMessage(message);
                }

                chatBox.appendChild(messageDiv);
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // Function to format bot messages with proper HTML
            function formatBotMessage(message) {
                // Convert markdown-like formatting to HTML
                let formatted = message
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/### (.*?)(\n|$)/g, '<h4>$1</h4>')
                    .replace(/## (.*?)(\n|$)/g, '<h4>$1</h4>')
                    .replace(/# (.*?)(\n|$)/g, '<h4>$1</h4>');

                // Convert numbered lists
                formatted = formatted.replace(/(\d+\.\s.*?)(?=\n\d+\.|\n\n|$)/gs, function(match) {
                    const items = match.split(/\n(?=\d+\.)/);
                    const listItems = items.map(item => {
                        const content = item.replace(/^\d+\.\s*/, '');
                        return `<li>${content}</li>`;
                    }).join('');
                    return `<ol>${listItems}</ol>`;
                });

                // Convert bullet points
                formatted = formatted.replace(/([•\-\*]\s.*?)(?=\n[•\-\*]|\n\n|$)/gs, function(match) {
                    const items = match.split(/\n(?=[•\-\*])/);
                    const listItems = items.map(item => {
                        const content = item.replace(/^[•\-\*]\s*/, '');
                        return `<li>${content}</li>`;
                    }).join('');
                    return `<ul>${listItems}</ul>`;
                });

                return formatted;
            }

            // Function to call Gemini API
            async function callGeminiAPI(userMessage) {
                if (!geminiApiKey) {
                    throw new Error('Please configure your Gemini API key first');
                }

                const prompt = `You are an expert career guidance counselor. When someone asks about career details or mentions their skills, provide comprehensive guidance including:

1. **Career Recommendation**: Suggest the most suitable career based on their skills/interests
2. **Complete Roadmap**: Provide a detailed step-by-step career roadmap
3. **Study Materials**: Recommend specific books, documentation, and resources
4. **Course Recommendations**: Suggest courses from platforms like Udemy, Coursera, edX, Pluralsight, etc.
5. **Interview Preparation**: Provide interview tips and common questions for that field

Format your response with clear headings and bullet points. Be specific and actionable.

User message: ${userMessage}`;

                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 2048,
                        }
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Gemini API Error: ${errorData.error?.message || 'Unknown error'}`);
                }

                const data = await response.json();

                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                    return data.candidates[0].content.parts[0].text;
                } else {
                    throw new Error('No response generated from Gemini API');
                }
            }

            // Function to handle user input
            async function handleUserInput() {
                const message = userInput.value.trim();

                if (message === '') return;

                if (geminiApiKey === 'YOUR_GEMINI_API_KEY_HERE') {
                    addMessage('⚠️ Please replace YOUR_GEMINI_API_KEY_HERE with your actual Gemini API key in the HTML file.');
                    return;
                }

                // Add user message to chat
                addMessage(message, true);

                // Clear input field
                userInput.value = '';

                // Show loading indicator
                loading.style.display = 'block';

                try {
                    // Call Gemini API
                    const response = await callGeminiAPI(message);

                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Add bot response
                    addMessage(response);

                    // Update chat history in database
                    await updateChatHistory(message, response);

                } catch (error) {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Show error message
                    addMessage(`❌ Error: ${error.message}`);
                    console.error('Error:', error);
                }
            }

            // Function to update chat history
            async function updateChatHistory(userMessage, botResponse) {
                try {
                    await fetch('/api/predict', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            skills: userMessage,
                            userId: currentUserId,
                            geminiResponse: botResponse
                        })
                    });
                } catch (error) {
                    console.error('Error updating chat history:', error);
                }
            }

            // Load all users function (simplified for Gemini version)
            function loadAllUsers() {
                sidebarLoading.style.display = 'block';
                userList.innerHTML = '';

                fetch('/api/users')
                    .then(response => response.json())
                    .then(data => {
                        sidebarLoading.style.display = 'none';

                        if (data.error) {
                            userList.innerHTML = `<li class="text-danger">Error: ${data.error}</li>`;
                            return;
                        }

                        if (!data.users || data.users.length === 0) {
                            userList.innerHTML = '<li class="text-muted">No users found</li>';
                            return;
                        }

                        // Add current user first
                        const currentUserItem = document.createElement('li');
                        currentUserItem.className = 'user-item active';
                        currentUserItem.dataset.userId = userId;

                        const currentUserData = data.users.find(user => user.userId === userId);
                        const currentChatTitle = currentUserData && currentUserData.chatTitle ?
                            currentUserData.chatTitle : "Current Chat";

                        currentUserItem.innerHTML = `
                            <div class="user-id">${currentChatTitle}</div>
                            <div class="timestamp">Current session</div>
                        `;
                        userList.appendChild(currentUserItem);

                        // Add other users
                        data.users.forEach(user => {
                            if (user.userId === userId) return;

                            const userItem = document.createElement('li');
                            userItem.className = 'user-item';
                            userItem.dataset.userId = user.userId;

                            const timestamp = new Date(user.timestamp);
                            const formattedDate = timestamp.toLocaleDateString();
                            const formattedTime = timestamp.toLocaleTimeString();

                            const chatTitle = user.chatTitle || `Chat ${user.userId.substring(0, 6)}...`;

                            userItem.innerHTML = `
                                <div class="user-id">${chatTitle}</div>
                                <div class="timestamp">Last active: ${formattedDate} ${formattedTime}</div>
                            `;
                            userList.appendChild(userItem);
                        });
                    })
                    .catch(error => {
                        sidebarLoading.style.display = 'none';
                        userList.innerHTML = `<li class="text-danger">Error loading users: ${error.message}</li>`;
                        console.error('Error loading users:', error);
                    });
            }

            // New Chat button event
            newChatBtn.addEventListener('click', function() {
                // Generate a new random user ID
                const newUserId = generateRandomId();

                // Clear chat box
                chatBox.innerHTML = '';

                // Add welcome message
                addMessage(`Hello! I'm your AI-powered career guidance assistant. I can help you with:

🎯 Career recommendations based on your skills
🗺️ Complete career roadmaps
📚 Study material recommendations
🎓 Course suggestions from platforms like Udemy, Coursera, etc.
💼 Interview preparation guidance

Tell me about your skills or ask about any career field!`);

                // Update current user ID
                currentUserId = newUserId;

                // Update active class in sidebar
                document.querySelectorAll('.user-item').forEach(i => i.classList.remove('active'));

                // Add new user to the list
                const newUserItem = document.createElement('li');
                newUserItem.className = 'user-item active';
                newUserItem.dataset.userId = newUserId;
                newUserItem.innerHTML = `
                    <div class="user-id">New Chat</div>
                    <div class="timestamp">Just now</div>
                `;

                // Add to the top of the list
                if (userList.firstChild) {
                    userList.insertBefore(newUserItem, userList.firstChild);
                } else {
                    userList.appendChild(newUserItem);
                }
            });

            // Generate a random ID for new chats
            function generateRandomId() {
                return Math.random().toString(36).substring(2, 10);
            }

            // Clear chat button event
            clearChatBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear your chat history? This cannot be undone.')) {
                    // Clear the chat box
                    chatBox.innerHTML = '';

                    // Add welcome message
                    addMessage(`Hello! I'm your AI-powered career guidance assistant. I can help you with:

🎯 Career recommendations based on your skills
🗺️ Complete career roadmaps
📚 Study material recommendations
🎓 Course suggestions from platforms like Udemy, Coursera, etc.
💼 Interview preparation guidance

Tell me about your skills or ask about any career field!`);

                    // Clear chat history in the database
                    fetch('/api/clear_chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            userId: currentUserId
                        })
                    })
                    .then(() => {
                        // Refresh the user list to show updated timestamps
                        loadAllUsers();
                    })
                    .catch(error => {
                        console.error('Error clearing chat:', error);
                    });
                }
            });

            // Event listeners
            sendBtn.addEventListener('click', handleUserInput);

            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleUserInput();
                }
            });

            // Load all users when page loads
            loadAllUsers();
        });
    </script>
</body>
</html>
