<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <title>Career Guidance Chatbot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }
        .main-container {
            height: 100vh;
            display: flex;
        }
        .sidebar {
            width: 280px;
            background-color: #2c3e50;
            color: white;
            padding: 15px;
            overflow-y: auto;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .sidebar-header {
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }
        .sidebar-header h5 {
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
            color: #ecf0f1;
        }
        .sidebar-header p {
            opacity: 0.7;
            font-size: 0.8rem;
        }
        .sidebar-toggle {
            position: fixed;
            left: 280px;
            top: 15px;
            background-color: #2c3e50;
            color: white;
            border: none;
            border-radius: 0 8px 8px 0;
            padding: 12px 8px;
            z-index: 1001;
            transition: all 0.3s;
            box-shadow: 3px 0 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .sidebar-toggle:hover {
            background-color: #3498db;
        }
        .sidebar-toggle i {
            font-size: 1.1rem;
        }
        .sidebar.collapsed {
            left: -280px;
        }
        .sidebar-toggle.collapsed {
            left: 0;
        }
        .user-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .user-item {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            background-color: rgba(255,255,255,0.05);
        }
        .user-item:hover {
            background-color: rgba(255,255,255,0.1);
            border-left-color: rgba(255,255,255,0.5);
            transform: translateX(2px);
        }
        .user-item.active {
            background-color: #3498db;
            border-left-color: #ffffff;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }
        .user-item .user-id {
            font-weight: 600;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            color: #ecf0f1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .user-item .user-id::before {
            content: '';
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #2ecc71;
            margin-right: 10px;
            box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
        }
        .user-item .timestamp {
            font-size: 0.75rem;
            color: rgba(255,255,255,0.7);
            margin-left: 18px;
            margin-top: 4px;
            font-style: italic;
        }
        .chat-container {
            flex: 1;
            padding: 25px;
            margin-left: 280px;
            transition: all 0.3s;
            max-width: 1200px;
            margin: 0 auto 0 280px;
            background-color: #f8f9fa;
        }
        .chat-container.expanded {
            margin-left: 0;
        }
        .chat-box {
            height: 450px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            background-color: white;
            margin-bottom: 20px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
        }
        .user-message {
            background-color: #3498db;
            color: white;
            border-radius: 18px 18px 0 18px;
            padding: 12px 18px;
            margin-bottom: 15px;
            max-width: 70%;
            margin-left: auto;
            box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
            animation: fadeIn 0.3s ease;
        }
        .bot-message {
            background-color: #f1f3f5;
            color: #343a40;
            border-radius: 18px 18px 18px 0;
            padding: 12px 18px;
            margin-bottom: 15px;
            max-width: 70%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border-left: 3px solid #3498db;
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .career-card {
            border: none;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .career-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .career-card h3 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #f1f3f5;
            padding-bottom: 10px;
        }
        .skill-badge {
            background-color: #6c757d;
            color: white;
            border-radius: 20px;
            padding: 6px 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            display: inline-block;
            font-size: 0.85rem;
            transition: all 0.2s ease;
        }
        .skill-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,0.1);
        }
        .recommended-skill {
            background-color: #27ae60;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 15px 0;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(52, 152, 219, 0.05);
        }
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            color: #3498db;
        }
        .azure-badge {
            background-color: #0078d4;
            color: white;
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
            margin-left: 10px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        .intent-badge {
            background-color: #9b59b6;
            color: white;
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
            margin-left: 10px;
            font-weight: 500;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            font-size: 0.85rem;
            color: #7f8c8d;
            border-top: 1px solid #e9ecef;
        }
        .btn {
            white-space: nowrap;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .btn:active {
            transform: translateY(1px);
        }
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
        .btn-danger {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }
        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
        }
        #newChatBtn {
            background-color: #27ae60;
            border-color: #27ae60;
            transition: all 0.2s;
            padding: 6px 12px;
            font-size: 0.9rem;
        }
        #newChatBtn:hover {
            background-color: #2ecc71;
            border-color: #2ecc71;
        }
        .input-group {
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: 10px;
            overflow: hidden;
        }
        .form-control {
            border: 1px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
        }
        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
            border-color: #3498db;
        }
        .results-container {
            margin-top: 20px;
        }
        .timestamp {
            font-size: 0.7em;
            color: #6c757d;
            margin-top: 5px;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .sidebar {
                left: -280px;
            }
            .sidebar-toggle {
                left: 0;
            }
            .chat-container {
                margin-left: 0;
            }
            .sidebar.active {
                left: 0;
            }
            .sidebar-toggle.active {
                left: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar for user chats -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="mb-0">Chat History</h5>
                    <button class="btn btn-sm btn-success" id="newChatBtn">
                        <i class="bi bi-plus-lg"></i> New Chat
                    </button>
                </div>
                <p>Select a chat to view its history</p>
            </div>
            <div id="sidebarLoading" class="text-center my-3">
                <div class="spinner-border spinner-border-sm text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Loading chats...</span>
            </div>
            <ul class="user-list" id="userList">
                <!-- User list will be populated here -->
            </ul>
        </div>

        <!-- Sidebar toggle button -->
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="bi bi-chevron-left" id="toggleIcon"></i>
        </button>

        <!-- Main chat area -->
        <div class="chat-container" id="chatContainer">
            <h1 class="text-center mb-4">Career Guidance Chatbot <span class="azure-badge">Azure AI</span></h1>

            <div class="d-flex justify-content-end align-items-center mb-4">
                <button class="btn btn-danger me-3" id="clearChatBtn">
                    <i class="bi bi-trash me-1"></i> Clear Chat
                </button>
                <div id="syncStatus" class="text-success me-2 d-flex align-items-center" style="display: none;">
                    <i class="bi bi-check-circle-fill me-1"></i>
                    <span>Synced across browsers</span>
                </div>
            </div>

            <div class="chat-box" id="chatBox">
                <!-- Chat messages will appear here -->
                <div class="bot-message" id="welcomeMessage">
                    Hello! I'm your career guidance assistant powered by Azure AI. Tell me about your skills, and I'll suggest potential career paths for you.
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Thinking...</span>
            </div>

            <div class="input-group mb-4">
                <input type="text" class="form-control" id="userInput" placeholder="Enter your skills or try 'hi', 'help', 'thank you', 'about'">
                <button class="btn btn-primary" type="button" id="sendBtn">
                    <i class="bi bi-send-fill me-1"></i> Send
                </button>
            </div>

            <div id="resultsContainer" class="results-container">
                <!-- Career prediction results will appear here -->
            </div>

            <div class="footer">
                Powered by Azure AI Services: Language Understanding, Azure Functions, and Cosmos DB
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Azure Function URLs and Keys
            const FUNCTION_URLS = {
                // Career Functions
                PREDICT: "https://careerbot-functions.azurewebsites.net/api/simple_predict",
                CAREER_DETAILS: "https://careerbot-functions.azurewebsites.net/api/get_career_details",
                RECOMMEND_SKILLS: "https://careerbot-functions.azurewebsites.net/api/recommend_skills",
                // Chat Functions
                SAVE_CHAT: "https://careerbot-chat-functions.azurewebsites.net/api/save_chat_history",
                GET_CHAT: "https://careerbot-chat-functions.azurewebsites.net/api/get_chat_history",
                GET_USERS: "https://careerbot-chat-functions.azurewebsites.net/api/get_all_users",
                // Language Service Function
                ANALYZE_TEXT: "https://careerbot-chat-functions.azurewebsites.net/api/analyze_text"
            };

            // Function Keys
            const FUNCTION_KEYS = {
                PREDICT: "fxJzyFRWCfmeg6xidPZ1U6hI1jKh-rs0PHssvoGVivdbAzFu73zBDg==",
                CAREER_DETAILS: "2hWpsaNthJq8phI-k1I-KAzLNue3PIEKbdh7YMuEbdjwAzFuLwZFlw==",
                RECOMMEND_SKILLS: "HyIr3_14UbWD9j15pik-MUVCWFZgiiOfGO52TOxZSMo9AzFuh8ZuWw==",
                SAVE_CHAT: "chnKFaQ0IFvmIHpnpM3S5aUOyo0V5Zm5yTJpMh7rOPIYAzFuszfWsw==",
                GET_CHAT: "BlDm4jWjI6C62fnK456lEbWvLmY9gz9eyyz7QyNZR_XxAzFuCp-MIQ==",
                GET_USERS: "mcaMJayDvRUuKFh3qu7xOhg0EVePOYGzU-MnNH019wA3AzFuOadvlQ==",
                ANALYZE_TEXT: "wyX-rdAoN2c9Mo_T1q1IKsox2Mqnwy5IdWSxUUjk09eQAzFu4Hah2w=="
            };

            // DOM elements
            const chatBox = document.getElementById('chatBox');
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');
            const loading = document.getElementById('loading');
            const resultsContainer = document.getElementById('resultsContainer');
            const clearChatBtn = document.getElementById('clearChatBtn');
            const syncStatus = document.getElementById('syncStatus');
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const toggleIcon = document.getElementById('toggleIcon');
            const chatContainer = document.getElementById('chatContainer');
            const userList = document.getElementById('userList');
            const sidebarLoading = document.getElementById('sidebarLoading');
            const newChatBtn = document.getElementById('newChatBtn');

            // Generate a unique user ID if not already stored
            let userId = localStorage.getItem('chatbotUserId');
            if (!userId) {
                userId = 'user-' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('chatbotUserId', userId);
            }
            let currentUserId = userId;

            // Show sync status
            syncStatus.style.display = 'block';

            // Load chat history from server
            loadChatHistory();

            // Load user list
            loadUserList();

            // Function to analyze text with Azure Language Service
            function analyzeTextWithLanguageService(text) {
                const analyzeUrl = `${FUNCTION_URLS.ANALYZE_TEXT}?code=${FUNCTION_KEYS.ANALYZE_TEXT}`;

                console.log("Analyzing text with Language Service:", text);
                console.log("URL:", analyzeUrl);

                return fetch(analyzeUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify({
                        text: text
                    })
                })
                .then(response => {
                    console.log("Language Service response status:", response.status);

                    if (!response.ok) {
                        throw new Error(`Language Service HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Full Language Service response:", data);

                    // Make sure all necessary properties exist even if not returned
                    return {
                        skills: data.skills || [],
                        intent: data.intent || "SkillsInput",
                        entities: data.entities || {},
                        response: data.response || null,
                        raw_result: data.raw_result || null
                    };
                })
                .catch(error => {
                    console.error('Error analyzing text with Language Service:', error);
                    // Return empty result if Language Service fails
                    return {
                        skills: [],
                        intent: "SkillsInput",
                        entities: {}
                    };
                });
            }

            // Function to handle user input
            function handleUserInput() {
                const userText = userInput.value.trim();
                if (!userText) return;

                // Add user message to chat
                addMessage(userText, true);

                // Clear input
                userInput.value = '';

                // Show loading indicator
                loading.style.display = 'flex';

                // Hide results container
                resultsContainer.innerHTML = '';

                console.log("Processing user input:", userText);

                // Check if the input looks like a greeting or simple command
                // This is a simple check - we'll use the Language Service to confirm
                const simpleInputPattern = /^(hi|hello|hey|help|about|thank you|thanks)$/i;
                if (simpleInputPattern.test(userText)) {
                    // If it looks like a greeting, use Language Service
                    analyzeTextWithLanguageService(userText)
                        .then(languageResult => {
                            console.log("Language analysis result:", languageResult);

                            // Check if we have a conversational intent (Greeting, Help, etc.)
                            if (languageResult.intent && languageResult.response) {
                                // Hide loading indicator
                                loading.style.display = 'none';

                                // Add the response from the language service
                                addMessage(languageResult.response);

                                // Save chat history
                                saveChat(userText, {
                                    conversational: true,
                                    language_result: languageResult
                                });

                                return;
                            }

                            // If it's not a conversational intent, fall back to prediction
                            callPredictionFunction(userText);
                        })
                        .catch(error => {
                            console.error("Error in language analysis:", error);
                            // Fall back to direct prediction if language service fails
                            callPredictionFunction(userText);
                        });
                } else {
                    // For all other inputs, directly call prediction without language analysis
                    callPredictionFunction(userText);
                }
            }

            // Function to call the prediction API directly
            function callPredictionFunction(userSkills, languageResult = null) {
                const predictUrl = `${FUNCTION_URLS.PREDICT}?code=${FUNCTION_KEYS.PREDICT}`;

                console.log("Calling prediction with URL:", predictUrl);
                console.log("Sending skills:", userSkills);

                fetch(predictUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        skills: userSkills
                    })
                })
                .then(response => {
                    console.log("Prediction response status:", response.status);
                    if (!response.ok) {
                        throw new Error(`Prediction HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Prediction response data:", data);

                    // Hide loading indicator
                    loading.style.display = 'none';

                    if (!data) {
                        console.log("No data returned from prediction");
                        addMessage(`Sorry, I couldn't process your request. Please try again.`);
                        return;
                    }

                    if (data.error) {
                        console.error("Error from prediction:", data.error);
                        addMessage(`Error: ${data.error}`);
                        return;
                    }

                    // Verify that we have predictions
                    if (!data.predictions || data.predictions.length === 0) {
                        console.error("No predictions in response:", data);
                        addMessage("I couldn't find any career matches for those skills. Could you provide more details about your skills?");
                        return;
                    }

                    // Add bot response for career prediction
                    const topCareer = data.predictions[0].career;
                    addMessage(`Based on your skills, I recommend exploring a career in ${topCareer}. Here are the details:`);

                    // Add language result to the data
                    if (languageResult) {
                        data.language_result = languageResult;
                    }

                    // Store user skills in the data for later use
                    data.user_skills = userSkills;

                    // Save chat history
                    saveChat(userSkills, data);
                    
                    // Automatically fetch career details for the top career and display results
                    getCareerDetailsAndDisplayResults(topCareer, data);
                })
                .catch(error => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Show error message
                    console.error('Error processing request:', error);
                    addMessage(`Sorry, an error occurred: ${error.message}`);

                    // Log useful debugging information
                    console.error('Full error details:', error);
                });
            }

            // Function to add a message to the chat
            function addMessage(message, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = isUser ? 'user-message' : 'bot-message';

                // Handle newlines in messages
                if (message.includes('\n')) {
                    message.split('\n').forEach((line, index) => {
                        if (index > 0) {
                            messageDiv.appendChild(document.createElement('br'));
                        }
                        messageDiv.appendChild(document.createTextNode(line));
                    });
                } else {
                    messageDiv.textContent = message;
                }

                // Add timestamp
                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date().toLocaleTimeString();
                messageDiv.appendChild(timestamp);

                chatBox.appendChild(messageDiv);

                // Scroll to bottom
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // Function to display career predictions
            function displayResults(data, careerSkills = []) {
                // Clear previous results
                resultsContainer.innerHTML = '';

                // Log the data for debugging
                console.log("Displaying results:", data);

                if (!data || !data.predictions || data.predictions.length === 0) {
                    console.error("No predictions data to display");
                    addMessage("I couldn't find any career predictions based on those skills. Please try providing more specific skills.");
                    return;
                }

                // Create card for top career
                const topCareer = data.predictions[0];
                const careerDetails = data.top_career_details || { skills: [] };

                const careerCard = document.createElement('div');
                careerCard.className = 'career-card';

                let careerHtml = `
                    <h3>Recommended Career: ${topCareer.career}`;

                // Add intent badge if language result is available
                if (data.language_result && data.language_result.intent) {
                    careerHtml += `<span class="intent-badge">Intent: ${data.language_result.intent}</span>`;
                }

                careerHtml += `</h3>
                    <p>Confidence: ${(topCareer.confidence * 100).toFixed(2)}%</p>
                    <h5>Common Skills for this Career:</h5>
                    <div class="mb-3">
                `;

                // Add skill badges - use the careerSkills if provided, otherwise use what's in careerDetails
                if (careerSkills && careerSkills.length > 0) {
                    careerSkills.forEach(skill => {
                        careerHtml += `<span class="skill-badge">${skill}</span>`;
                    });
                } else if (careerDetails && careerDetails.skills && careerDetails.skills.length > 0) {
                    const topSkills = careerDetails.skills.slice(0, 15); // Show top 15 skills
                    topSkills.forEach(skill => {
                        careerHtml += `<span class="skill-badge">${skill}</span>`;
                    });
                } else {
                    careerHtml += `<p>No specific skills information available for this career.</p>`;
                }

                careerHtml += `
                    </div>
                    <h5>Other Potential Careers:</h5>
                    <ul>
                `;

                // Add other career options
                if (data.predictions.length > 1) {
                    data.predictions.slice(1).forEach(pred => {
                        careerHtml += `<li>${pred.career} (${(pred.confidence * 100).toFixed(2)}%)</li>`;
                    });
                } else {
                    careerHtml += `<li>No other strong career matches found</li>`;
                }

                careerHtml += `</ul>`;

                // Add Azure info if available (but don't require it)
                if (data.language_result) {
                    careerHtml += `
                        <div class="mt-3 pt-3 border-top">
                            <small class="text-muted">Detected by Azure AI Language Service:</small>
                    `;

                    // Show intent if available
                    if (data.language_result.intent) {
                        careerHtml += `
                            <div class="mb-2">
                                <strong>Intent:</strong> <span class="intent-badge">${data.language_result.intent}</span>
                            </div>
                        `;
                    }

                    // Show detected skills
                    if (data.language_result.skills && data.language_result.skills.length > 0) {
                        careerHtml += `
                            <div>
                                <strong>Detected skills:</strong>
                                <div class="mt-1">
                        `;

                        data.language_result.skills.forEach(skill => {
                            careerHtml += `<span class="skill-badge">${skill}</span>`;
                        });

                        careerHtml += `</div></div>`;
                    }

                    // Show detected entities if available
                    if (data.language_result.entities && Object.keys(data.language_result.entities).length > 0) {
                        careerHtml += `
                            <div class="mt-2">
                                <strong>Detected entities:</strong>
                                <div class="mt-1">
                        `;

                        // Loop through all entity types
                        for (const [entityType, entities] of Object.entries(data.language_result.entities)) {
                            entities.forEach(entity => {
                                careerHtml += `<span class="skill-badge">${entity} <small>(${entityType})</small></span>`;
                            });
                        }

                        careerHtml += `</div></div>`;
                    }

                    careerHtml += `</div>`;
                } else {
                    // If no language service results, add a note about direct prediction
                    careerHtml += `
                        <div class="mt-3 pt-3 border-top">
                            <small class="text-muted">Prediction based on direct input</small>
                        </div>
                    `;
                }

                careerCard.innerHTML = careerHtml;
                resultsContainer.appendChild(careerCard);
            }

            // Function to get career details and display results immediately
            function getCareerDetailsAndDisplayResults(career, predictionData) {
                // Show loading indicator
                loading.style.display = 'flex';

                const careerDetailsUrl = `${FUNCTION_URLS.CAREER_DETAILS}?code=${FUNCTION_KEYS.CAREER_DETAILS}&career=${encodeURIComponent(career)}`;

                console.log("Calling career details with URL:", careerDetailsUrl);

                fetch(careerDetailsUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                })
                .then(response => {
                    console.log("Career details response status:", response.status);
                    console.log("Career details response headers:", response.headers);

                    if (!response.ok) {
                        if (response.status === 0) {
                            throw new Error("Network error or CORS issue. Check browser console for details.");
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    console.log("Career details response data:", data);

                    if (data.error) {
                        addMessage(`Error: ${data.error}`);
                        displayResults(predictionData); // Display with original data
                        return;
                    }

                    // If we have skills, display them in the card
                    if (data.skills && data.skills.length > 0) {
                        const topSkills = data.skills.slice(0, 10); // Only top 10 skills
                        displayResults(predictionData, topSkills);
                    } else {
                        displayResults(predictionData); // Display with original data
                    }
                })
                .catch(error => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Display results with original data on error
                    displayResults(predictionData);

                    // Log the error with more details
                    console.error('Error getting career details:', error);
                    console.error('URL:', careerDetailsUrl);
                });
            }

            // Function to save chat history
            function saveChat(userMessage, botResponse) {
                const userId = currentUserId || 'test-user-123';

                // Create messages array for the new API format
                let messages = [];

                // Get existing messages from chat box
                const chatMessages = chatBox.querySelectorAll('.user-message, .bot-message');

                // Convert to the format expected by the API
                chatMessages.forEach(msg => {
                    const isUser = msg.classList.contains('user-message');
                    const content = msg.textContent.replace(/\d{1,2}:\d{2}:\d{2}(?: [AP]M)?$/, '').trim(); // Remove timestamp

                    messages.push({
                        role: isUser ? "user" : "assistant",
                        content: content
                    });
                });

                // Generate a chat title from the first user message
                let chatTitle = null;
                for (let msg of messages) {
                    if (msg.role === "user") {
                        chatTitle = msg.content.substring(0, 30) + (msg.content.length > 30 ? "..." : "");
                        break;
                    }
                }

                const saveChatUrl = `${FUNCTION_URLS.SAVE_CHAT}?code=${FUNCTION_KEYS.SAVE_CHAT}`;

                console.log("Saving chat history for user:", userId);
                console.log("Chat title:", chatTitle);
                console.log("Number of messages:", messages.length);

                fetch(saveChatUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        messages: messages,
                        chat_title: chatTitle
                    })
                })
                .then(response => {
                    console.log("Save chat response status:", response.status);

                    if (!response.ok) {
                        if (response.status === 0) {
                            throw new Error("Network error or CORS issue when saving chat.");
                        } else {
                            throw new Error(`HTTP error when saving chat! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Chat saved successfully:", data);
                })
                .catch(error => {
                    console.error('Error saving chat history:', error);
                    console.error('URL:', saveChatUrl);
                    // Don't show error to user during normal operation to avoid disrupting the experience
                });
            }

            // Function to load chat history
            function loadChatHistory() {
                const getChatUrl = `${FUNCTION_URLS.GET_CHAT}?code=${FUNCTION_KEYS.GET_CHAT}&user_id=${currentUserId}`;

                console.log("Loading chat history for user:", currentUserId);
                console.log("URL:", getChatUrl);

                fetch(getChatUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                })
                .then(response => {
                    console.log("Get chat response status:", response.status);

                    if (!response.ok) {
                        if (response.status === 0) {
                            throw new Error("Network error or CORS issue when loading chat history.");
                        } else {
                            throw new Error(`HTTP error when loading chat history! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Chat history loaded:", data);

                    if (data.chat_history && data.chat_history.length > 0) {
                        // Clear default welcome message
                        chatBox.innerHTML = '';

                        // Add messages from history
                        data.chat_history.forEach(msg => {
                            addMessage(msg.content, msg.role === 'user');
                        });

                        console.log(`Loaded ${data.chat_history.length} messages from history`);
                    } else {
                        console.log("No chat history found or empty history");
                    }
                })
                .catch(error => {
                    console.error('Error loading chat history:', error);
                    console.error('URL:', getChatUrl);
                    // Don't show error to user during normal operation to avoid disrupting the experience
                });
            }

            // Function to load user list
            function loadUserList() {
                const getUsersUrl = `${FUNCTION_URLS.GET_USERS}?code=${FUNCTION_KEYS.GET_USERS}`;

                console.log("Loading user list");
                console.log("URL:", getUsersUrl);

                fetch(getUsersUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                })
                .then(response => {
                    console.log("Get users response status:", response.status);

                    if (!response.ok) {
                        if (response.status === 0) {
                            throw new Error("Network error or CORS issue when loading users.");
                        } else {
                            throw new Error(`HTTP error when loading users! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Users loaded:", data);

                    // Hide loading indicator
                    sidebarLoading.style.display = 'none';

                    if (data.users && data.users.length > 0) {
                        userList.innerHTML = '';

                        console.log(`Found ${data.users.length} users`);

                        // Add users to list
                        data.users.forEach(user => {
                            const li = document.createElement('li');
                            li.className = 'user-item';
                            if (user.userId === currentUserId) {
                                li.classList.add('active');
                            }

                            // Format timestamp
                            let timestamp = 'No date';
                            if (user.timestamp) {
                                const date = new Date(user.timestamp);
                                timestamp = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                            }

                            // Use chat title or default
                            const chatTitle = user.chatTitle || 'Chat from ' + timestamp;

                            li.textContent = chatTitle;
                            li.setAttribute('data-user-id', user.userId);

                            li.addEventListener('click', function() {
                                // Set current user
                                currentUserId = user.userId;

                                console.log("Switching to user:", currentUserId);

                                // Update active class
                                document.querySelectorAll('.user-item').forEach(item => {
                                    item.classList.remove('active');
                                });
                                this.classList.add('active');

                                // Load chat history for this user
                                loadChatHistory();
                            });

                            userList.appendChild(li);
                        });
                    } else {
                        console.log("No users found");
                        userList.innerHTML = '<li class="text-center">No chat history found</li>';
                    }
                })
                .catch(error => {
                    // Hide loading indicator
                    sidebarLoading.style.display = 'none';

                    // Show error message
                    userList.innerHTML = `<li class="text-center">Error loading users: ${error.message}</li>`;

                    // Log the error with more details
                    console.error('Error loading user list:', error);
                    console.error('URL:', getUsersUrl);
                });
            }

            // Function to recommend skills
            function recommendSkills(userSkills) {
                // Show loading indicator
                loading.style.display = 'flex';

                // Add message to chat
                addMessage(`Finding skill recommendations based on your profile...`);

                const recommendSkillsUrl = `${FUNCTION_URLS.RECOMMEND_SKILLS}?code=${FUNCTION_KEYS.RECOMMEND_SKILLS}`;

                console.log("Calling recommend skills with URL:", recommendSkillsUrl);
                console.log("User skills:", userSkills);

                // Convert userSkills to array if it's a string
                const skillsData = typeof userSkills === 'string'
                    ? { skills: userSkills.split(',').map(s => s.trim()) }
                    : { skills: userSkills };

                console.log("Skills data being sent:", skillsData);

                fetch(recommendSkillsUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(skillsData)
                })
                .then(response => {
                    console.log("Recommend skills response status:", response.status);
                    console.log("Recommend skills response headers:", response.headers);

                    if (!response.ok) {
                        if (response.status === 0) {
                            throw new Error("Network error or CORS issue. Check browser console for details.");
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    console.log("Recommend skills response data:", data);

                    if (data.error) {
                        addMessage(`Error: ${data.error}`);
                        return;
                    }

                    if (!data.recommended_skills || data.recommended_skills.length === 0) {
                        addMessage(`I couldn't find any specific skill recommendations based on your profile.`);
                        return;
                    }

                    // Add bot response with skill recommendations
                    let message = `Here are skills I recommend learning to enhance your career prospects:\n`;
                    message += data.recommended_skills.slice(0, 5).join(', ');

                    addMessage(message);

                    // Create a card with more detailed information
                    const skillsCard = document.createElement('div');
                    skillsCard.className = 'career-card';

                    let skillsHtml = `
                        <h3>Recommended Skills</h3>
                        <p>Based on your current skills: ${userSkills}</p>
                        <h5>Skills to Learn:</h5>
                        <div class="mb-3">
                    `;

                    // Add all recommended skills as badges
                    data.recommended_skills.forEach(skill => {
                        skillsHtml += `<span class="skill-badge recommended-skill">${skill}</span>`;
                    });

                    skillsHtml += `</div>`;

                    skillsCard.innerHTML = skillsHtml;
                    resultsContainer.appendChild(skillsCard);
                })
                .catch(error => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Show detailed error message
                    const errorMessage = `Sorry, an error occurred while recommending skills: ${error.message}`;
                    addMessage(errorMessage);

                    // Log the error with more details
                    console.error('Error recommending skills:', error);
                    console.error('URL:', recommendSkillsUrl);
                    console.error('Request data:', skillsData);

                    // Suggest a solution
                    addMessage("This might be due to a CORS issue. Please check if CORS is enabled on the Azure Function or try using a CORS proxy.");
                });
            }

            // Keep the original getCareerDetails function for backward compatibility
            function getCareerDetails(career) {
                // Show loading indicator
                loading.style.display = 'flex';

                // Add message to chat
                addMessage(`Getting details about ${career}...`);

                const careerDetailsUrl = `${FUNCTION_URLS.CAREER_DETAILS}?code=${FUNCTION_KEYS.CAREER_DETAILS}&career=${encodeURIComponent(career)}`;

                console.log("Calling career details with URL:", careerDetailsUrl);

                fetch(careerDetailsUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                })
                .then(response => {
                    console.log("Career details response status:", response.status);
                    console.log("Career details response headers:", response.headers);

                    if (!response.ok) {
                        if (response.status === 0) {
                            throw new Error("Network error or CORS issue. Check browser console for details.");
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    console.log("Career details response data:", data);

                    if (data.error) {
                        addMessage(`Error: ${data.error}`);
                        return;
                    }

                    // Add bot response with career details
                    let message = `Here are details about ${career}:\n`;

                    if (data.skills && data.skills.length > 0) {
                        message += `Common skills: ${data.skills.slice(0, 10).join(', ')}`;
                        addMessage(message);
                    } else {
                        message += "No specific skills information available for this career.";
                        addMessage(message);
                    }
                })
                .catch(error => {
                    // Hide loading indicator
                    loading.style.display = 'none';

                    // Show detailed error message
                    const errorMessage = `Sorry, an error occurred while getting career details: ${error.message}`;
                    addMessage(errorMessage);

                    // Log the error with more details
                    console.error('Error getting career details:', error);
                    console.error('URL:', careerDetailsUrl);

                    // Suggest a solution
                    addMessage("This might be due to a CORS issue. Please check if CORS is enabled on the Azure Function or try using a CORS proxy.");
                });
            }

            // Function to clear chat
            function clearChat() {
                // Clear chat box
                chatBox.innerHTML = '';

                // Add welcome message
                addMessage("Hello! I'm your career guidance assistant powered by Azure AI. Tell me about your skills, and I'll suggest potential career paths for you.");

                // Clear results container
                resultsContainer.innerHTML = '';

                // Clear chat history on server
                const clearChatUrl = `${FUNCTION_URLS.SAVE_CHAT}?code=${FUNCTION_KEYS.SAVE_CHAT}`;

                fetch(clearChatUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentUserId,
                        messages: [],
                        chat_title: "New Chat"
                    })
                })
                .catch(error => {
                    console.error('Error clearing chat history:', error);
                });
            }

            // Function to create a new chat
            function createNewChat() {
                // Generate a new user ID
                currentUserId = 'user-' + Math.random().toString(36).substring(2, 15);
                localStorage.setItem('chatbotUserId', currentUserId);

                // Clear chat box
                chatBox.innerHTML = '';

                // Add welcome message
                addMessage("Hello! I'm your career guidance assistant powered by Azure AI. Tell me about your skills, and I'll suggest potential career paths for you.");

                // Clear results container
                resultsContainer.innerHTML = '';

                // Reload user list
                loadUserList();
            }

            // Toggle sidebar
            function toggleSidebar() {
                sidebar.classList.toggle('collapsed');
                chatContainer.classList.toggle('expanded');
                sidebarToggle.classList.toggle('collapsed');

                if (sidebar.classList.contains('collapsed')) {
                    toggleIcon.classList.remove('bi-chevron-left');
                    toggleIcon.classList.add('bi-chevron-right');
                } else {
                    toggleIcon.classList.remove('bi-chevron-right');
                    toggleIcon.classList.add('bi-chevron-left');
                }
            }

            // Event listeners
            sendBtn.addEventListener('click', handleUserInput);

            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleUserInput();
                }
            });

            clearChatBtn.addEventListener('click', clearChat);

            newChatBtn.addEventListener('click', createNewChat);

            sidebarToggle.addEventListener('click', toggleSidebar);

            // Debug utility - accessible via browser console with 'debugCareerbot.testAllConnections()'
            window.debugCareerbot = {
                testAllConnections: async function() {
                    console.log("Testing all API connections...");

                    const results = {};

                    // Test analyze_text
                    try {
                        console.log("Testing analyze_text...");
                        const analyzeUrl = `${FUNCTION_URLS.ANALYZE_TEXT}?code=${FUNCTION_KEYS.ANALYZE_TEXT}`;
                        const analyzeResponse = await fetch(analyzeUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ text: "Python programming" })
                        });
                        const analyzeData = await analyzeResponse.json();
                        results.analyze_text = {
                            status: analyzeResponse.status,
                            ok: analyzeResponse.ok,
                            data: analyzeData
                        };
                        console.log("Analyze text result:", results.analyze_text);
                    } catch (error) {
                        results.analyze_text = { error: error.message };
                        console.error("Analyze text error:", error);
                    }

                    // Test predict
                    try {
                        console.log("Testing prediction...");
                        const predictUrl = `${FUNCTION_URLS.PREDICT}?code=${FUNCTION_KEYS.PREDICT}`;
                        const predictResponse = await fetch(predictUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ skills: "Python, Data Analysis" })
                        });
                        const predictData = await predictResponse.json();
                        results.predict = {
                            status: predictResponse.status,
                            ok: predictResponse.ok,
                            data: predictData
                        };
                        console.log("Prediction result:", results.predict);
                    } catch (error) {
                        results.predict = { error: error.message };
                        console.error("Prediction error:", error);
                    }

                    // Test career details
                    try {
                        console.log("Testing career details...");
                        const careerDetailsUrl = `${FUNCTION_URLS.CAREER_DETAILS}?code=${FUNCTION_KEYS.CAREER_DETAILS}&career=Data Scientist`;
                        const careerResponse = await fetch(careerDetailsUrl);
                        const careerData = await careerResponse.json();
                        results.career_details = {
                            status: careerResponse.status,
                            ok: careerResponse.ok,
                            data: careerData
                        };
                        console.log("Career details result:", results.career_details);
                    } catch (error) {
                        results.career_details = { error: error.message };
                        console.error("Career details error:", error);
                    }

                    // Test recommend skills
                    try {
                        console.log("Testing recommend skills...");
                        const recommendSkillsUrl = `${FUNCTION_URLS.RECOMMEND_SKILLS}?code=${FUNCTION_KEYS.RECOMMEND_SKILLS}`;
                        const skillsResponse = await fetch(recommendSkillsUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ skills: ["Python", "Data Analysis"] })
                        });
                        const skillsData = await skillsResponse.json();
                        results.recommend_skills = {
                            status: skillsResponse.status,
                            ok: skillsResponse.ok,
                            data: skillsData
                        };
                        console.log("Recommend skills result:", results.recommend_skills);
                    } catch (error) {
                        results.recommend_skills = { error: error.message };
                        console.error("Recommend skills error:", error);
                    }

                    console.log("All API tests completed:", results);
                    return results;
                },

                checkApiUrls: function() {
                    console.log("API URLs:");
                    console.log("ANALYZE_TEXT:", FUNCTION_URLS.ANALYZE_TEXT);
                    console.log("PREDICT:", FUNCTION_URLS.PREDICT);
                    console.log("CAREER_DETAILS:", FUNCTION_URLS.CAREER_DETAILS);
                    console.log("RECOMMEND_SKILLS:", FUNCTION_URLS.RECOMMEND_SKILLS);
                    console.log("SAVE_CHAT:", FUNCTION_URLS.SAVE_CHAT);
                    console.log("GET_CHAT:", FUNCTION_URLS.GET_CHAT);
                    console.log("GET_USERS:", FUNCTION_URLS.GET_USERS);

                    return FUNCTION_URLS;
                }
            };
        });
    </script>
</body>
</html>
