# Career Recommendation Chatbot

A chatbot application that recommends careers based on user skills and provides career details.

## Features

- Predicts suitable careers based on user skills
- Provides detailed information about different careers
- Recommends additional skills for career advancement
- Maintains chat history for users

## Technologies Used

- Python
- Flask
- Azure Functions
- Machine Learning (TF-IDF, Random Forest, SVM, Logistic Regression)
- Azure Cosmos DB

## Deployment

The application is deployed on Azure using:
- Azure Functions for the backend API
- Azure App Service for the web interface

## API Endpoints

- `/api/predict_career`: Predicts careers based on skills
- `/api/get_career_details`: Gets details about a specific career
- `/api/recommend_skills`: Recommends additional skills for a career

## Local Development

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run locally: `python app.py`

## Azure Deployment

See the deployment guides in the repository for detailed instructions on deploying to Azure.
